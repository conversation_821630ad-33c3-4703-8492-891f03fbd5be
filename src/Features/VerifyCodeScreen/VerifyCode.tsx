import { useState, useRef, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, Image, StatusBar } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import Ionicons from '@expo/vector-icons/Ionicons';
import { ErrorMessage } from '../../../components/ErrorMessage';

type RootStackParamList = {
  Login: undefined;
  ForgotPassword: undefined;
  PasswordReset: undefined;
  VerifyCode: undefined;
};

type VerifyCodeScreenNavigationProp = StackNavigationProp<RootStackParamList, 'VerifyCode'>;

export function VerifyCode() {
  const navigation = useNavigation<VerifyCodeScreenNavigationProp>();
  const [code, setCode] = useState('');
  const [timeLeft, setTimeLeft] = useState(140); // 2:20 in seconds
  const [errorMessage, setErrorMessage] = useState('');
  const [showError, setShowError] = useState(false);
  const inputRef = useRef<TextInput>(null);

  // Default OTP
  const DEFAULT_OTP = '1234';

  // Countdown timer effect
  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => {
        setTimeLeft(timeLeft - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [timeLeft]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleCodeChange = (value: string) => {
    // Only allow numbers and limit to reasonable OTP length
    const numericValue = value.replace(/[^0-9]/g, '');
    if (numericValue.length <= 6) {
      setCode(numericValue);
      // Clear error when user starts typing
      if (showError) {
        setShowError(false);
        setErrorMessage('');
      }
    }
  };

  const handleVerify = () => {
    if (code.trim().length >= 4) {
      // Check if the entered code matches the default OTP
      if (code.trim() === DEFAULT_OTP) {
        // Success - clear any previous errors
        setShowError(false);
        setErrorMessage('');
        console.log('Verification successful!');
        // Navigate to password reset or success screen
        // navigation.navigate('PasswordReset');
      } else {
        // Show error message
        setErrorMessage('Your OTP is wrong');
        setShowError(true);
        console.log('Verification failed. Expected:', DEFAULT_OTP, 'Got:', code);
      }
    }
  };

  const handleResend = () => {
    // Handle resend logic here
    console.log('Resending code...');
    setTimeLeft(140); // Reset timer
    setCode(''); // Clear current code
    setShowError(false); // Clear any error messages
    setErrorMessage('');
  };

  return (
    <View className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />

      {/* Header with Back Button */}
      <View className="flex-row items-center px-6 pb-4 pt-20">
        <TouchableOpacity className="flex-row items-center" onPress={() => navigation.goBack()}>
          <Ionicons name="chevron-back" size={18} color="#656565" style={{ marginRight: 8 }} />
          <Text className="font-oxanium-medium text-text-secondary" style={{ fontSize: 18 }}>
            Back to Forgot Password
          </Text>
        </TouchableOpacity>
      </View>

      {/* Main Container */}
      <View className="flex-1 px-6 pt-8">
        {/* Logo Section */}
        <View className="items-center" style={{ marginBottom: 60 }}>
          <Image
            source={require('../../../assets/Logo.png')}
            style={{ width: 200, height: 64, marginBottom: 40 }}
            resizeMode="contain"
          />
        </View>

        {/* Title and Description */}
        <View style={{ marginBottom: 40 }}>
          <Text
            className="text-center font-oxanium-bold text-text-primary"
            style={{ fontSize: 40, fontWeight: '600', marginBottom: 20, lineHeight: 40 }}>
            Verify code
          </Text>

          <Text
            className="text-center font-oxanium-medium text-text-secondary"
            style={{ fontSize: 18, fontWeight: '400', lineHeight: 24, paddingHorizontal: 20 }}>
            An authentication code has been sent to your email.
          </Text>
        </View>

        {/* Code Input Section */}
        <View style={{ marginBottom: 32 }}>
          <Text
            className="font-oxanium-medium text-text-primary"
            style={{ fontSize: 16, fontWeight: '400', marginBottom: 16 }}>
            Enter Code
          </Text>

          <TextInput
            ref={inputRef}
            className={`rounded-lg border bg-background-input text-text-primary ${
              showError ? 'border-error' : 'border-border'
            }`}
            style={{
              height: 56,
              borderWidth: 1,
              paddingHorizontal: 16,
              fontSize: 16,
              textAlign: 'left',
              fontFamily: 'Oxanium-Medium',
            }}
            placeholder="enter your OTP"
            placeholderTextColor="#9CA3AF"
            value={code}
            onChangeText={handleCodeChange}
            keyboardType="number-pad"
            maxLength={6}
            autoFocus={true}
          />

          {/* Error Message */}
          <ErrorMessage message={errorMessage} visible={showError} />
        </View>

        {/* Resend Section */}
        <View className="flex-row items-center justify-between" style={{ marginBottom: 40 }}>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Text className="font-oxanium-medium text-text-secondary" style={{ fontSize: 16 }}>
              Didn&apos;t receive a code?
            </Text>
            <TouchableOpacity onPress={handleResend}>
              <Text
                className="font-oxanium-medium text-primary"
                style={{ fontSize: 16, marginLeft: 6 }}>
                Resend
              </Text>
            </TouchableOpacity>
          </View>
          <View
            className="items-center justify-center rounded-full border border-error-light bg-white"
            style={{ width: 30, height: 30 }}>
            <Text className="font-oxanium-medium text-text-secondary" style={{ fontSize: 12 }}>
              {formatTime(timeLeft)}
            </Text>
          </View>
        </View>

        {/* Verify Button */}
        <TouchableOpacity
          className="items-center justify-center"
          style={{
            height: 56,
            backgroundColor: code.trim().length >= 4 ? '#3C9D9B' : 'white',
            borderWidth: 2,
            borderColor: '#3C9D9B',
            borderRadius: 28,
            marginBottom: 40,
          }}
          onPress={handleVerify}
          disabled={code.trim().length < 4}>
          <Text
            className="font-oxanium-bold"
            style={{
              fontSize: 18,
              fontWeight: '600',
              color: code.trim().length >= 4 ? 'white' : '#3C9D9B',
            }}>
            Verify
          </Text>
        </TouchableOpacity>

        {/* Bottom Link */}
        <View className="flex-1 items-center justify-end" style={{ paddingBottom: 40 }}>
          <TouchableOpacity>
            <Text
              className="font-oxanium-bold text-text-primary underline"
              style={{ fontSize: 16, fontWeight: '400' }}>
              Log into Estate Control
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

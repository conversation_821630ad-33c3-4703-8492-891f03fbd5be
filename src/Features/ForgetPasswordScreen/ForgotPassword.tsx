import { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, Image, StatusBar, Keyboard, TouchableWithoutFeedback } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import { ErrorMessage } from '../../../components';
import Ionicons from '@expo/vector-icons/Ionicons';

type RootStackParamList = {
  Login: undefined;
  ForgotPassword: undefined;
  PasswordReset: undefined;
  VerifyCode: undefined;
};

type ForgotPasswordScreenNavigationProp = StackNavigationProp<RootStackParamList, 'ForgotPassword'>;

type RecoveryMethod = 'email' | 'phone' | 'whatsapp';

export function ForgotPassword() {
  const navigation = useNavigation<ForgotPasswordScreenNavigationProp>();
  const [selectedMethod, setSelectedMethod] = useState<RecoveryMethod>('email');
  const [contactInfo, setContactInfo] = useState('');
  const [showError, setShowError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  // Email validation function
  const validateEmail = (email: string): boolean => {
    // More comprehensive email validation
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email.trim());
  };

  // Phone validation function
  const validatePhone = (phone: string): boolean => {
    // Remove all non-digit characters except + for international format
    const cleanedPhone = phone.replace(/[^\d+]/g, '');

    // Check if it starts with + (international) or is a local number
    if (cleanedPhone.startsWith('+')) {
      // International format: + followed by 7-15 digits
      return /^\+[1-9]\d{6,14}$/.test(cleanedPhone);
    } else {
      // Local format: 7-15 digits
      return /^[1-9]\d{6,14}$/.test(cleanedPhone);
    }
  };

  const handleInputChange = (value: string) => {
    setContactInfo(value);
    if (showError && value.trim()) {
      setShowError(false);
      setErrorMessage('');
    }
  };

  const validateContactInfo = (): boolean => {
    if (!contactInfo.trim()) {
      setErrorMessage('Please enter your contact information to continue.');
      setShowError(true);
      return false;
    }

    if (selectedMethod === 'email') {
      if (!validateEmail(contactInfo.trim())) {
        setErrorMessage('Please enter a valid email address.');
        setShowError(true);
        return false;
      }
    } else if (selectedMethod === 'phone') {
      if (!validatePhone(contactInfo.trim())) {
        setErrorMessage('Please enter a valid phone number.');
        setShowError(true);
        return false;
      }
    } else if (selectedMethod === 'whatsapp') {
      if (!validatePhone(contactInfo.trim())) {
        setErrorMessage('Please enter a valid WhatsApp number.');
        setShowError(true);
        return false;
      }
    }

    return true;
  };

  const handleSubmit = () => {
    if (!validateContactInfo()) {
      return;
    }

    setShowError(false);
    setErrorMessage('');
    // Handle password recovery logic here
    console.log('Recovery method:', selectedMethod);
    console.log('Contact info:', contactInfo);

    // Navigate to verification code screen
    navigation.navigate('VerifyCode');
  };

  const getPlaceholderText = () => {
    switch (selectedMethod) {
      case 'email':
        return 'Enter your email address';
      case 'phone':
        return 'Enter your phone number';
      case 'whatsapp':
        return 'Enter your WhatsApp number';
      default:
        return 'Enter your contact information';
    }
  };

  const getKeyboardType = () => {
    switch (selectedMethod) {
      case 'email':
        return 'email-address';
      case 'phone':
      case 'whatsapp':
        return 'phone-pad';
      default:
        return 'default';
    }
  };

  const RadioButton = ({
    selected,
    onPress,
    label,
  }: {
    selected: boolean;
    onPress: () => void;
    label: string;
  }) => (
    <TouchableOpacity className="mr-6 flex-row items-center" onPress={onPress}>
      <View
        className="items-center justify-center"
        style={{
          width: 20,
          height: 20,
          borderRadius: 10,
          borderWidth: 2,
          borderColor: '#3C9D9B',
          marginRight: 8,
          backgroundColor: 'transparent',
        }}>
        {selected && (
          <View
            style={{
              width: 10,
              height: 10,
              borderRadius: 5,
              backgroundColor: '#3C9D9B',
            }}
          />
        )}
      </View>
      <Text
        className="font-oxanium-medium text-text-primary"
        style={{ fontSize: 16, fontWeight: '400' }}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <View className="flex-1 bg-white">
        <StatusBar barStyle="dark-content" backgroundColor="white" />

        {/* Header with Back Button */}
        <View className="flex-row items-center px-6 pb-4 pt-20">
          <TouchableOpacity className="flex-row items-center" onPress={() => navigation.goBack()}>
            <Ionicons name="chevron-back" size={18} color="#6B7280" style={{ marginRight: 8 }} />
            <Text className="font-oxanium-medium text-text-secondary" style={{ fontSize: 18 }}>
              Back to login
            </Text>
          </TouchableOpacity>
        </View>

        {/* Main Container */}
        <View className="flex-1 px-6 pt-8">
          {/* Logo Section */}
          <View className="items-center" style={{ marginBottom: 60 }}>
            <Image
              source={require('../../../assets/Logo.png')}
              style={{ width: 200, height: 64, marginBottom: 40 }}
              resizeMode="contain"
            />
          </View>

          {/* Title and Description */}
          <View style={{ marginBottom: 40 }}>
            <Text
              className="text-center font-oxanium-bold text-text-primary"
              style={{ fontSize: 40, fontWeight: '600', marginBottom: 20, lineHeight: 40 }}>
              Forgot your password?
            </Text>

            <Text
              className="text-center font-oxanium-medium text-text-secondary"
              style={{ fontSize: 18, fontWeight: '400', lineHeight: 20, paddingHorizontal: 20 }}>
              Don&apos;t worry, happens to all of us. Enter your email or phone number below to
              recover your password
            </Text>
          </View>

          {/* Radio Button Options */}
          <View className="flex-row justify-center" style={{ marginBottom: 32 }}>
            <RadioButton
              selected={selectedMethod === 'email'}
              onPress={() => setSelectedMethod('email')}
              label="Email"
            />
            <RadioButton
              selected={selectedMethod === 'phone'}
              onPress={() => setSelectedMethod('phone')}
              label="Phone Number"
            />
            <RadioButton
              selected={selectedMethod === 'whatsapp'}
              onPress={() => setSelectedMethod('whatsapp')}
              label="WhatsApp"
            />
          </View>

          {/* Input Field */}
          <View style={{ marginBottom: 32 }}>
            <TextInput
              className="rounded-lg border-border bg-background-input text-text-primary"
              style={{
                height: 56,
                borderWidth: 1,
                paddingHorizontal: 16,
                fontSize: 16,
                marginBottom: showError ? 8 : 0,
                fontFamily: 'Oxanium-Medium',
              }}
              placeholder={getPlaceholderText()}
              placeholderTextColor="#9CA3AF"
              value={contactInfo}
              onChangeText={handleInputChange}
              keyboardType={getKeyboardType()}
              returnKeyType="done"
              onSubmitEditing={Keyboard.dismiss}
              blurOnSubmit={true}
            />

            <ErrorMessage message={errorMessage} visible={showError} />
          </View>

          {/* Submit Button */}
          <TouchableOpacity
            className="items-center justify-center"
            style={{
              height: 56,
              backgroundColor: contactInfo.trim() ? '#3C9D9B' : 'white',
              borderWidth: 2,
              borderColor: '#3C9D9B',
              borderRadius: 28,
              marginBottom: 40,
            }}
            onPress={handleSubmit}>
            <Text
              className="font-oxanium-bold"
              style={{
                fontSize: 18,
                fontWeight: '600',
                color: contactInfo.trim() ? 'white' : '#3C9D9B',
              }}>
              Submit
            </Text>
          </TouchableOpacity>

          {/* Bottom Link */}
          <View className="flex-1 items-center justify-end" style={{ paddingBottom: 40 }}>
            <TouchableOpacity>
              <Text
                className="font-oxanium-bold text-text-primary underline"
                style={{ fontSize: 16, fontWeight: '400' }}>
                Log into Estate Control
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
}
